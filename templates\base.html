<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>Image-Based_Crack_Prediction</title>
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='site.css')}}" />
    <style>
        /* Add a black background color to the top navigation */
        .topnav {
            background-color: #333;
            overflow: hidden;
        }

        /* Style the links inside the navigation bar */
        .topnav a {
            float: left;
            color: #f2f2f2;
            text-align: center;
            padding: 14px 16px;
            text-decoration: none;
            font-size: 17px;
        }

        /* Change the color of links on hover */
        .topnav a:hover {
            background-color: #ddd;
            color: black;
        }

        /* Add a color to the active/current link */
        .topnav a.active {
            background-color: #04AA6D;
            color: white;
        }

        /* Split layout container */
        .split-container {
            display: flex;
            height: 100vh; /* Full viewport height */
        }

        .split-container .left-side {
            flex: 1;
        }
        .split-container .right-side {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            padding: 20px;
        }
        html, body {
        height: 100%;
        margin: 0;
        background-image: url('{{ url_for('static', filename='fig1.png') }}');
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        background-attachment: fixed;
        }

    </style>
</head>
<body>
    <div class="topnav">
        <a href="{{url_for('home')}}">HOME</a>
        <a href="{{ url_for('list') }}">CLIENTSLIST</a>
        <a href="{{ url_for('login') }}">CLIENTLOGIN</a>
        <a href="{{ url_for('enternew') }}" class="navbar-item">CLIENTSINUP</a>

        <!-- <a href="#">student register</a> -->
    </div>

    <div class="body-content">
        {% block content %}
        {% endblock %}
        <hr/>
    </div>
    
    <div class="footer">
        <center><p>&copy; 2024 Your Website. All rights reserved. In NavyaItSolutions</p></center>
    </div>
</body>
</html>
