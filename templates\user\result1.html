<!DOCTYPE html>
<html lang="en">
<head>
    <title>User Dashboard - Deep Crack Prediction</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        @keyframes floatingBackground {
            0% { background-position: 0% 0%; }
            50% { background-position: 100% 100%; }
            100% { background-position: 0% 0%; }
        }

        @keyframes crackSpread {
            0% { width: 0; height: 0; opacity: 1; transform: scale(1) rotate(0deg); }
            50% { width: 300px; height: 4px; opacity: 0.95; transform: scale(1.4) rotate(15deg); }
            100% { width: 0; height: 0; opacity: 0; transform: scale(1) rotate(-15deg); }
        }

        @keyframes shardBurst {
            0% { transform: translate(0, 0) rotate(0deg); opacity: 1; }
            100% { transform: translate(${Math.random() * 100 - 50}px, ${Math.random() * 100 - 50}px) rotate(${Math.random() * 360}deg); opacity: 0; }
        }

        @keyframes crackFade {
            0% { opacity: 1; transform: scale(1) rotate(0deg); }
            100% { opacity: 0; transform: scale(1.6) rotate(30deg); }
        }

        @keyframes glowPulse {
            0% { text-shadow: 0 0 10px rgba(135, 206, 235, 0.8), 0 0 20px rgba(70, 130, 180, 0.5); }
            50% { text-shadow: 0 0 25px rgba(135, 206, 235, 1), 0 0 35px rgba(70, 130, 180, 0.7); }
            100% { text-shadow: 0 0 10px rgba(135, 206, 235, 0.8), 0 0 20px rgba(70, 130, 180, 0.5); }
        }

        @keyframes wave {
            0% { transform: translateY(0) skew(0deg); }
            50% { transform: translateY(-30px) skew(4deg); }
            100% { transform: translateY(0) skew(0deg); }
        }

        @keyframes shimmer {
            0% { background-position: -300% 0; }
            100% { background-position: 300% 0; }
        }

        @keyframes orbit {
            0% { transform: rotate(0deg) translateX(25px) rotate(0deg); }
            100% { transform: rotate(360deg) translateX(25px) rotate(-360deg); }
        }

        @keyframes ripple {
            0% { transform: scale(0); opacity: 0.8; }
            100% { transform: scale(2); opacity: 0; }
        }

        body {
            font-family: 'Poppins', sans-serif;
            text-align: center;
            background: linear-gradient(105deg, #87CEEB, #4682B4, #87CEFA, #4169E1);
            background-size: 400% 400%;
            animation: floatingBackground 10s infinite alternate;
            margin: 0;
            padding: 0;
            color: #ffffff;
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            position: relative;
            overflow: hidden;
        }

        .overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at center, rgba(135, 206, 235, 0.25), rgba(70, 130, 180, 0.15), rgba(0, 0, 0, 0.1), transparent 70%);
            pointer-events: none;
            z-index: 0;
        }

        header {
            background: linear-gradient(90deg, rgba(0, 0, 0, 0.95), rgba(135, 206, 235, 0.9), rgba(70, 130, 180, 0.85));
            padding: 30px;
            font-size: 24px;
            text-shadow: 0 0 25px rgba(135, 206, 235, 0.8);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            position: relative;
            z-index: 10;
            border-bottom: 4px solid rgba(135, 206, 235, 0.4);
        }

        header h1 {
            margin: 0;
            animation: glowPulse 2s infinite alternate;
            position: relative;
            color: #040202;
        }

        header h1::before, header h1::after {
            content: '';
            position: absolute;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            top: 50%;
            animation: orbit 5s linear infinite;
        }

        header h1::before {
            background: #87CEEB;
            left: -30px;
        }

        header h1::after {
            background: #4682B4;
            right: -30px;
            animation-direction: reverse;
        }

        nav {
            display: flex;
            justify-content: flex-end;
            padding: 15px 30px;
            background: linear-gradient(90deg, rgba(0, 0, 0, 0.9), rgba(239, 14, 2, 0.85), rgba(116, 2, 239, 0.8));
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
            z-index: 10;
        }

        .button-container {
            display: flex;
            gap: 10px;
        }

        .button-container button {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            background: white;
            color: #000000;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease-in-out;
            box-shadow: 0 4px 6px rgba(135, 206, 235, 0.5);
            position: relative;
            overflow: hidden;
            z-index: 1;
        }

        .button-container button::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(120deg, transparent, rgba(135, 206, 235, 0.5), transparent);
            animation: shimmer 3s infinite;
            z-index: -1;
        }

        .button-container button:hover {
            background: linear-gradient(45deg, #4169E1, #87CEFA, #4682B4);
            transform: translateY(-5px);
            box-shadow: 0 6px 12px rgba(135, 206, 235, 0.7);
        }

        .user-content {
            margin: 30px auto;
            max-width: 1200px;
            text-align: center;
            position: relative;
            z-index: 1;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .user-content h2 {
            font-size: 36px;
            text-shadow: 0 0 30px rgba(135, 206, 235, 0.8);
            animation: wave 5.5s infinite;
            background: linear-gradient(90deg, #87CEEB, #4682B4, #87CEFA);
            -webkit-background-clip: text;
            background-clip: text;
            color: black;
        }

        .user-content p {
            font-size: 18px;
            opacity: 0.9;
            text-shadow: 0 0 15px rgba(135, 206, 235, 0.6);
        }

        .user-card {
            background: rgba(0, 0, 0, 0.7);
            padding: 25px;
            margin: 15px;
            border-radius: 10px;
            box-shadow: 0 6px 10px rgba(0, 0, 0, 0.2);
            transition: transform 0.4s ease-in-out, box-shadow 0.4s ease-in-out;
        }

        .user-card:hover {
            transform: scale(1.1) rotate(2deg);
            box-shadow: 0 15px 30px rgba(135, 206, 235, 0.8);
        }

        .user-card h3 {
            color: #87CEEB;
            margin: 0 0 10px 0;
        }

        .user-card p {
            color: #ffffff;
            font-size: 16px;
            text-shadow: none;
        }

        .crack-line {
            position: absolute;
            background: linear-gradient(90deg, transparent, #87CEEB, #4682B4, transparent);
            pointer-events: none;
            z-index: 5;
            animation: crackSpread 1s ease-out forwards;
            box-shadow: 0 0 20px rgba(135, 206, 235, 0.7);
        }

        .crack-shard {
            position: absolute;
            width: 10px;
            height: 10px;
            background: radial-gradient(circle, #87CEEB, transparent);
            pointer-events: none;
            z-index: 5;
            animation: shardBurst 1.5s ease-out forwards;
            border-radius: 50%;
        }

        .crack-effect {
            position: absolute;
            width: 220px;
            height: 220px;
            background: url('./static/2.webp') no-repeat;
            background-size: contain;
            pointer-events: none;
            animation: crackFade 2.2s forwards;
            z-index: 2;
            filter: drop-shadow(0 0 15px rgba(135, 206, 235, 0.8));
        }

        .ripple-effect {
            position: absolute;
            width: 100px;
            height: 100px;
            border-radius: 50%;
            border: 2px solid rgba(135, 206, 235, 0.5);
            pointer-events: none;
            z-index: 4;
            animation: ripple 1.2s ease-out forwards;
        }

        .particle {
            position: absolute;
            border-radius: 50%;
            pointer-events: none;
            animation: floatUp 9s ease forwards;
            z-index: 0;
            background: radial-gradient(circle, rgba(135, 206, 235, 0.8), transparent);
        }

        .orbit-particle {
            position: absolute;
            width: 10px;
            height: 10px;
            background: #87CEEB;
            border-radius: 50%;
            animation: orbit 4s linear infinite;
        }

        footer {
            background: linear-gradient(90deg, rgba(0, 0, 0, 0.95), rgba(135, 206, 235, 0.9), rgba(70, 130, 180, 0.85));
            padding: 15px;
            font-size: 14px;
            text-shadow: 0 0 12px rgba(135, 206, 235, 0.6);
            box-shadow: 0 -10px 35px rgba(0, 0, 0, 0.2);
            z-index: 10;
            border-top: 4px solid rgba(135, 206, 235, 0.4);
            color: #070404;
        }
        
    </style>
</head>
<body>
    <div class="overlay"></div>
    <header>
        <h1>Deep Crack Prediction</h1>
    </header>
    
    <nav>
        <div class="button-container">
            <button onclick="window.location.href='/user_home'">Home</button>
            <button onclick="window.location.href='/training'">Training</button>
            <button onclick="window.location.href='/upload_file'">Start Prediction</button>
            <button onclick="window.location.href='/logout'">Logout</button>
        </div>
    </nav>
<style>
    .result-table {
    width: 100%;
    max-width: 400px;
    margin: 20px auto;
    border-collapse: collapse;
    text-align: left;
    background: rgba(0, 0, 0, 0.7);
    color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 6px 10px rgba(0, 0, 0, 0.2);
}

.result-table th, .result-table td {
    padding: 12px 15px;
    border-bottom: 1px solid rgba(135, 206, 235, 0.5);
}

.result-table th {
    background: rgba(135, 206, 235, 0.2);
    font-weight: bold;
    text-align: center;
}

.result-table tr:last-child td {
    border-bottom: none;
}

</style>
    <main class="user-content">
        <section>
            <div class="split-container">
                <div class="left-side"></div>
                <div class="right-side">
                    <div class="prediction-result">
                        <h1>Prediction Result</h1>
                        <img src="{{ url_for('static', filename='uploads/' + filename) }}" alt="Uploaded Image">
    
                        <table class="result-table">
                            <tr>
                                <th>Predicted Class</th>
                                <td>Crack detected!</td>
                            </tr>
                            {% if length and depth %}
                            <tr>
                                <th>Length</th>
                                <td>{{ length }} mm</td>
                            </tr>
                            <tr>
                                <th>Depth</th>
                                <td>{{ depth }} mm</td>
                            </tr>
                            {% else %}
                            <p>No crack detected.</p>
                        {% endif %}
                        </table>
    
                        <a href="{{ url_for('upload_file') }}">Upload another image</a>
                    </div>
                </div>
            </div>
        </section>
    </main>
    

    <footer>
        <p>© 2025 Deep Crack Prediction. All rights reserved.</p>
    </footer>

    <script>
        let lastCrackTime = 0;
        const colors = ['#87CEEB', '#4682B4', '#87CEFA', '#4169E1'];

        document.addEventListener("mousemove", (e) => {
            const now = Date.now();
            if (now - lastCrackTime > 100) {
                const crackLine = document.createElement("div");
                crackLine.classList.add("crack-line");
                const randomColor = colors[Math.floor(Math.random() * colors.length)];
                crackLine.style.background = `linear-gradient(90deg, transparent, ${randomColor}, ${colors[(colors.indexOf(randomColor) + 1) % colors.length]}, transparent)`;
                crackLine.style.left = `${e.clientX - 150}px`;
                crackLine.style.top = `${e.clientY}px`;
                crackLine.style.transform = `rotate(${Math.random() * 360}deg)`;
                crackLine.style.boxShadow = `0 0 20px ${randomColor}`;
                document.body.appendChild(crackLine);
                setTimeout(() => crackLine.remove(), 1000);

                for (let i = 0; i < 3; i++) {
                    const shard = document.createElement("div");
                    shard.classList.add("crack-shard");
                    shard.style.left = `${e.clientX}px`;
                    shard.style.top = `${e.clientY}px`;
                    shard.style.background = `radial-gradient(circle, ${colors[i % colors.length]}, transparent)`;
                    document.body.appendChild(shard);
                    setTimeout(() => shard.remove(), 1500);
                }

                const ripple = document.createElement("div");
                ripple.classList.add("ripple-effect");
                ripple.style.left = `${e.clientX - 50}px`;
                ripple.style.top = `${e.clientY - 50}px`;
                document.body.appendChild(ripple);
                setTimeout(() => ripple.remove(), 1200);

                if (Math.random() > 0.5) {
                    const crack = document.createElement("div");
                    crack.classList.add("crack-effect");
                    crack.style.left = `${e.clientX - 110}px`;
                    crack.style.top = `${e.clientY - 110}px`;
                    document.body.appendChild(crack);
                    setTimeout(() => crack.remove(), 2200);
                }
                lastCrackTime = now;
            }
        });

        function createParticle(x, y, sizeMultiplier = 1, color = null) {
            const particle = document.createElement("div");
            particle.classList.add("particle");
            const size = (Math.random() * 15 + 8) * sizeMultiplier;
            particle.style.width = `${size}px`;
            particle.style.height = `${size}px`;
            const particleColor = color || colors[Math.floor(Math.random() * colors.length)];
            particle.style.background = `radial-gradient(circle, ${particleColor}, transparent)`;
            particle.style.left = `${x || Math.random() * 100}vw`;
            particle.style.top = `${y || Math.random() * 100}vh`;
            document.body.appendChild(particle);
            setTimeout(() => particle.remove(), 9000);
        }
        setInterval(() => createParticle(), 200);

        document.addEventListener("mousemove", (e) => {
            if (Math.random() > 0.5) {
                createParticle(e.clientX / window.innerWidth * 100, e.clientY / window.innerHeight * 100, 0.8);
            }
        });

        const header = document.querySelector("header");
        const footer = document.querySelector("footer");
        for (let i = 0; i < 4; i++) {
            const orbitParticle = document.createElement("div");
            orbitParticle.classList.add("orbit-particle");
            orbitParticle.style.top = `${50 + Math.sin(i * 2) * 25}%`;
            orbitParticle.style.left = `${-35 + Math.cos(i * 2) * 25}px`;
            orbitParticle.style.background = colors[i % colors.length];
            orbitParticle.style.animationDelay = `${i * 0.5}s`;
            header.appendChild(orbitParticle.cloneNode(true));
            footer.appendChild(orbitParticle);
        });

        const buttons = document.querySelectorAll(".button-container button");
        buttons.forEach(button => {
            button.addEventListener("mouseenter", () => {
                button.style.transition = "all 0.3s ease";
                button.style.transform = "translateY(-5px) scale(1.06)";
            });
            button.addEventListener("mouseleave", () => {
                button.style.transform = "translateY(0) scale(1)";
            });
            button.addEventListener("click", () => {
                button.style.transition = "transform 0.2s";
                button.style.transform = "translateY(-8px)";
                setTimeout(() => {
                    button.style.transform = "translateY(0)";
                }, 200);
                const rect = button.getBoundingClientRect();
                for (let i = 0; i < 10; i++) {
                    createParticle(
                        (rect.left + rect.width / 2) / window.innerWidth * 100,
                        (rect.top + rect.height / 2) / window.innerHeight * 100,
                        0.7,
                        colors[i % colors.length]
                    );
                }
            });
        });

        const main = document.querySelector(".user-content");
        main.addEventListener("mouseenter", () => {
            main.querySelector("h2").style.animation = "wave 2s infinite";
        });
        main.addEventListener("mouseleave", () => {
            main.querySelector("h2").style.animation = "wave 5.5s infinite";
        });

        const styleSheet = document.createElement("style");
        styleSheet.textContent = `
            @keyframes floatUp {
                0% { transform: translateY(0) translateX(0); opacity: 1; }
                100% { transform: translateY(-${Math.random() * 80 + 60}vh) translateX(${Math.random() * 50 - 25}vw); opacity: 0; }
            }
        `;
        document.head.appendChild(styleSheet);
    </script>
</body>
</html>